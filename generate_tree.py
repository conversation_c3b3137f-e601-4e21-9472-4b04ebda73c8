#!/usr/bin/env python3
"""
生成项目目录树结构
"""
import os
from pathlib import Path

def generate_tree(directory, prefix="", max_depth=3, current_depth=0):
    """生成目录树结构"""
    if current_depth >= max_depth:
        return ""
    
    tree_str = ""
    path = Path(directory)
    
    # 获取所有文件和目录，排序
    items = sorted([item for item in path.iterdir() if not item.name.startswith('.')])
    
    for i, item in enumerate(items):
        is_last = i == len(items) - 1
        
        # 跳过一些不需要显示的目录和文件
        skip_items = {
            '__pycache__', '.git', '.vscode', '.idea', 
            'node_modules', '.pytest_cache', '.mypy_cache',
            '.ruff_cache', 'dist', 'build', '*.pyc'
        }
        
        if item.name in skip_items or item.name.endswith('.pyc'):
            continue
            
        # 构建当前行的前缀
        current_prefix = "└── " if is_last else "├── "
        tree_str += f"{prefix}{current_prefix}{item.name}\n"
        
        # 如果是目录且不是最后一层，递归处理
        if item.is_dir() and current_depth < max_depth - 1:
            extension = "    " if is_last else "│   "
            tree_str += generate_tree(
                item, 
                prefix + extension, 
                max_depth, 
                current_depth + 1
            )
    
    return tree_str

def main():
    """主函数"""
    print("VeighNa 4.0 量化交易项目目录结构")
    print("=" * 50)
    print("vnpy4/")
    
    # 生成目录树
    tree = generate_tree(".", max_depth=4)
    print(tree)
    
    # 保存到文件
    with open("项目目录树.txt", "w", encoding="utf-8") as f:
        f.write("VeighNa 4.0 量化交易项目目录结构\n")
        f.write("=" * 50 + "\n")
        f.write("vnpy4/\n")
        f.write(tree)
    
    print("\n目录树已保存到 '项目目录树.txt' 文件")

if __name__ == "__main__":
    main()
